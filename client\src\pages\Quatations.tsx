import { useState, useEffect } from "react";
import { Link, useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { fetchAPI } from "@/lib/api";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useToast } from "@/hooks/use-toast";
import { Plus, Calculator, FileCheck, SquarePen, Send, PlusCircle } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

const Quotations = () => {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [location, navigate] = useLocation();
  const [filteredOptions, setFilteredOptions] = useState<any[]>([]);
  const queryClient = useQueryClient();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showAddItemDialog, setShowAddItemDialog] = useState(false);
  const [selectedQuotation, setSelectedQuotation] = useState<any>(null);
  const [itemType, setItemType] = useState<"service" | "part">("service");
  const [quotations, setQuotations] = useState<any[]>([]);

  // Form states
  const [customerOpen, setCustomerOpen] = useState(false);
  const [quotationForm, setQuotationForm] = useState({
    customerId: "",
    vehicleId: "",
    description: "",
    validUntil: "",

    // Contact Details
    customerEmail: "",
    customerPhone: "",
    customerAddress: "",

    // Vehicle Details
    vehicleMake: "",
    vehicleModel: "",
    vehicleYear: "",
    vehicleRegistration: "",
    vehicleVin: "",
    vehicleMileage: "",
    vehicleColor: "",

    // Service Details
    serviceType: "Maintenance",
    priority: "Normal",
    estimatedDuration: "",

    // Financial
    subtotal: "0",
    taxPercentage: "5",
    taxAmount: "0",
    discountPercentage: "0",
    discountAmount: "0",
    total: "0",

    // Additional Info
    notes: "",
    internalNotes: "",
    warranty: "",
    termsAndConditions:
      "1. Quote valid for 30 days from date of issue.\n2. Prices are subject to change without notice.\n3. Work will commence only after written approval.\n4. Payment terms: 50% advance, balance on completion.",

    // Insurance/Warranty
    isInsuranceClaim: false,
    insuranceProvider: "",
    claimNumber: "",
  });

  const [itemForm, setItemForm] = useState({
    serviceId: "",
    partId: "",
    quantity: "1",
    price: "",
    description: "",
  });

  const fetchQuotations = async (term: string) => {
    try {
      const response = await fetchAPI(
        `/gate-pass/quotation/list?keyword=${encodeURI(term)}`,
        {
          method: "GET",
        }
      );
      // let options: any[] = [];
      if (response.ok) {
        const data = await response.json();
        // if (Array.isArray(data) && data.length > 0) {
        //   options = data.map((record: any) => ({
        //     id: record.id,
        //     description: record.jobDescription,
        //     vehicleId: record.vehicleId,
        //     vehicleNumber: record.vehicleRegistrationNumber,
        //     make: record.make || "",
        //     model_model: record.model_model || "",
        //     createdAt: record.createdAt,
        //     customerName: record.customerName,
        //   }));
        setQuotations(data);
        return;
      }
      setQuotations([]);
    } catch (error) {
      setQuotations([]);
    }
  };

  const revisitQuotation = (quotationId: number) => {
    navigate(`/quotation/revisit/${quotationId}`);
  };

  const approveQuotation = (quotationId: number) => {
    fetchAPI(`/gate-pass/quotation/approve/${quotationId}`, {
      method: "POST",
    }).then((response) => {
      if (response.ok) {
        fetchQuotations("");
        toast({
          title: "Quotation Approved",
          description:
            "The quotation has been approved and sent to the customer",
        });
      } else {
        toast({
          title: "Failed to approve quotation",
          description:
            "There was an error approving the quotation. Please try again.",
          variant: "destructive",
        });
      }
    });
  };

  useEffect(() => {
    fetchQuotations("");
  }, []);

  // Fetch customers
  const { data: customers } = useQuery({
    queryKey: ["/api/customers"],
    queryFn: async () => {
      const response = await fetch("/api/customers");
      if (!response.ok) throw new Error("Failed to fetch customers");
      return response.json();
    },
  });

  // Fetch vehicles
  const { data: vehicles } = useQuery({
    queryKey: ["/api/vehicles"],
    queryFn: async () => {
      const response = await fetch("/api/vehicles");
      if (!response.ok) throw new Error("Failed to fetch vehicles");
      return response.json();
    },
  });

  // Fetch services
  const { data: services } = useQuery({
    queryKey: ["/api/services"],
    queryFn: async () => {
      const response = await fetch("/api/services");
      if (!response.ok) throw new Error("Failed to fetch services");
      return response.json();
    },
  });

  // Fetch parts
  const { data: parts } = useQuery({
    queryKey: ["/api/parts"],
    queryFn: async () => {
      const response = await fetch("/api/parts");
      if (!response.ok) throw new Error("Failed to fetch parts");
      return response.json();
    },
  });

  const resetItemForm = () => {
    setItemForm({
      serviceId: "",
      partId: "",
      quantity: "1",
      price: "",
      description: "",
    });
  };

  const handleAddItem = () => {
    if (!selectedQuotation) return;

    if (itemType === "service" && !itemForm.serviceId) {
      toast({
        title: "Missing Information",
        description: "Please select a service",
        variant: "destructive",
      });
      return;
    }

    if (itemType === "part" && !itemForm.partId) {
      toast({
        title: "Missing Information",
        description: "Please select a part",
        variant: "destructive",
      });
      return;
    }

    const data =
      itemType === "service"
        ? {
            serviceId: parseInt(itemForm.serviceId),
            quantity: parseInt(itemForm.quantity),
            price: parseFloat(itemForm.price),
            description: itemForm.description,
          }
        : {
            partId: parseInt(itemForm.partId),
            quantity: parseInt(itemForm.quantity),
            price: parseFloat(itemForm.price),
            description: itemForm.description,
          };

    addItemMutation.mutate({ quotationId: selectedQuotation.id, data });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "bg-gray-100 text-gray-800";
      case "SENT":
        return "bg-blue-100 text-blue-800";
      case "APPROVED":
        return "bg-green-100 text-green-800";
      case "REJECTED":
        return "bg-red-100 text-red-800";
      case "EXPIRED":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getCustomerName = (customerId: number) => {
    const customer = customers?.find((c: any) => c.id === customerId);
    return customer?.name || "Unknown Customer";
  };

  const getVehicleInfo = (vehicleId: number) => {
    const vehicle = vehicles?.find((v: any) => v.id === vehicleId);
    return vehicle
      ? `${vehicle.make} ${vehicle.model} (${vehicle.registrationNumber})`
      : "Unknown Vehicle";
  };

  // Search license plate function
  const searchLicensePlate = async (term: string) => {
    if (!term || term.length < 2) {
      setFilteredOptions([]);
      return;
    }
    try {
      const response = await fetchAPI(
        `/gate-pass/list?keyword=${encodeURI(term)}`,
        {
          method: "GET",
        }
      );
      let options: any[] = [];
      if (response.ok) {
        const data = await response.json();
        if (Array.isArray(data) && data.length > 0) {
          options = data.map((record: any) => ({
            id: record.id,
            description: record.jobDescription,
            vehicleId: record.vehicleId,
            vehicleNumber: record.vehicleRegistrationNumber,
            make: record.make || "",
            model_model: record.model_model || "",
            createdAt: record.createdAt,
            customerName: record.customerName,
          }));
          setFilteredOptions(options);
          return;
        }
      }
      setFilteredOptions([]);
    } catch (error) {
      setFilteredOptions([]);
    }
  };

  const printJobCard = (quotationId: number) => {
    const printWindow = window.open("", "_blank");
    if (!printWindow) {
      toast({
        title: "Print Error",
        description:
          "Unable to open print window. Please check your popup blocker settings.",
        variant: "destructive",
      });
      return;
    }

    fetchAPI(`/gate-pass/quotation/details/${quotationId}`, {
      method: "GET",
    })
      .then((response) => {
        if (response.ok) {
          response.json().then((jobCard) => {
            // Calculate total cost from parts and services
            const calculateTotalCost = () => {
              const serviceCost = jobCard.servicesUsed
                ? jobCard.servicesUsed.reduce(
                    (total, service) => total + parseFloat(service.price || 0),
                    0
                  )
                : 0;
              const partsCost = jobCard.partsUsed
                ? jobCard.partsUsed.reduce(
                    (total, part) => total + parseFloat(part.price || 0),
                    0
                  )
                : 0;
              return serviceCost + partsCost;
            };

            // Use parts and services from the job card
            printWindow.document.write(`
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Job Card Invoice</title>
  <style>
    * { box-sizing: border-box; }
    html, body { height: 100%; margin: 0; font-family: Arial, sans-serif; font-size: 13px; }
    body { display: flex; flex-direction: column; padding: 30px; }
    .content { flex: 1; }
    .header { text-align: center; font-weight: bold; font-size: 16px; }
    .sub-header { text-align: center; font-size: 12px; margin-bottom: 20px; }
    table { width: 100%; border-collapse: collapse; border: 1px solid black }
    .details-table td { border: 1px solid #000; padding: 6px; vertical-align: top; }
    .billing-table th { border: 1px solid #000; padding: 10px; vertical-align: top; height: 40px; background-color: #f2f2f2; text-align: left; }
    .billing-table td { border-bottom: None; border-right: 1px solid black; padding: 10px; vertical-align: top; height: 40px; }
    .summary-table td { border-bottom: None; padding: 10px; height: 30px; }
    .summary-table .label { text-align: right; width: 85%; }
    .summary-table .value { text-align: right; width: 15%; border: 1px solid #000; }
    .grand-total-row { font-weight: bold; }
    .declaration { border-top: 1px solid #000; margin-top: 30px; padding-top: 10px; }
    .signature { margin-top: 60px; text-align: right; }
  </style>
</head>
<body>
  <div class="content">
    <div class="header">
      M R S AUTO MAINTENANCE L.L.C
    </div>
    <div class="sub-header">
      Tel: +971 55 994 1284, +971 55 994 1285 | Email: <EMAIL>
    </div>
    <h2 style="text-align: center;">INVOICE</h2>
    <table class="details-table">
      <tr>
        <td><strong>JOBCARD ID :</strong> ${jobCard?.id || ""}</td>
        <td><strong>Date:</strong> ${
          jobCard?.createdAt
            ? new Date(jobCard.createdAt).toLocaleDateString()
            : ""
        }</td>
        <td><strong>Time:</strong> ${
          jobCard?.createdAt
            ? new Date(jobCard.createdAt).toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })
            : ""
        }</td>
      </tr>
      <tr>
        <td><strong>Driver Name:</strong> ${
          jobCard?.gatePass?.customerName || ""
        }</td>
        <td><strong>Make & Model:</strong> ${
          jobCard?.vehicle?.model_model || ""
        }</td>
        <td><strong>Assigned to:</strong> ${"Unassigned"}</td>
      </tr>
      <tr>
        <td><strong>Chassis No:</strong> ${
          jobCard?.vehicle?.chassisNumber || ""
        }</td>
        <td><strong>Engine No:</strong> ${
          jobCard?.vehicle?.engineNumber || ""
        }</td>
        <td><strong>Reg No:</strong> ${
          jobCard?.vehicle?.vehicleRegistrationNumber || ""
        }</td>
      </tr>
      <tr>
        <td><strong>Delivery Date:</strong> ${
          jobCard?.gatePass?.estimatedDeliveryDate
            ? new Date(
                jobCard.gatePass.estimatedDeliveryDate
              ).toLocaleDateString()
            : ""
        }</td>
        <td><strong>Color:</strong> ${jobCard?.vehicle?.color || ""}</td>
        <td><strong>ODO Meter:</strong> ${jobCard?.gatePass?.mileage || ""}</td>
      </tr>
      <tr>
        <td><strong>Mobile No:</strong> ${jobCard?.gatePass?.phone || ""}</td>
        <td colspan="2"><strong>Veh. Reg. Card:</strong> ${
          jobCard?.vehicle?.registrationCard || "N"
        }</td>
      </tr>
    </table>
    <br>
    <table class="billing-table">
      <thead>
        <tr>
          <th style="width: 5%;">S.N</th>
          <th style="width: 55%;">Description</th>
          <th style="width: 10%;">Qty</th>
          <th style="width: 10%;">Unit</th>
          <th style="width: 10%;">Price</th>
          <th style="width: 10%;">Amount (AED)</th>
        </tr>
      </thead>
      <tbody>
        ${[...(jobCard.partsUsed || []), ...(jobCard.servicesUsed || [])]
          .map(
            (item, idx) => `
            <tr>
              <td>${idx + 1}</td>
              <td>${item.itemName || ""}</td>
              <td>1.00</td>
              <td>Pcs</td>
              <td>${parseFloat(item.price || 0).toFixed(2)}</td>
              <td>${parseFloat(item.price || 0).toFixed(2)}</td>
            </tr>
          `
          )
          .join("")}
        ${(() => {
          const totalRows =
            (jobCard.partsUsed?.length || 0) +
            (jobCard.servicesUsed?.length || 0);
          let emptyRows = "";
          for (let i = totalRows; i < 4; i++) {
            emptyRows += `<tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td></tr>`;
          }
          return emptyRows;
        })()}
      </tbody>
    </table>
    <br>
    <table class="summary-table">
      <tr>
        <td class="label">Total</td>
        <td class="value">${calculateTotalCost().toFixed(2)}</td>
      </tr>
      <tr>
        <td class="label">Less : Excess</td>
        <td class="value">0.00</td>
      </tr>
      <tr>
        <td class="label">Less : Discount</td>
        <td class="value">0.00</td>
      </tr>
      <tr>
        <td class="label">Add : VAT @ 5.00%</td>
        <td class="value">${(calculateTotalCost() * 0.05).toFixed(2)}</td>
      </tr>
      <tr class="grand-total-row">
        <td class="label">Grand Total</td>
        <td class="value">${(calculateTotalCost() * 1.05).toFixed(2)}</td>
      </tr>
    </table>
    <br><strong>Dirhams ${
      Math.round(calculateTotalCost() * 1.05) || "Zero"
    } Only</strong>
  </div>
  <div class="declaration">
    <strong>DECLARATION</strong><br>
    I hereby authorize your garage to repair my vehicle.<br><br>
    Name: ______________________ <br>
    Signature: __________________
  </div>
  <div class="signature">
    for <strong>M R S Auto Maintenance LLC</strong><br>
    Authorised Signatory
  </div>
</body>
</html>
`);

            setTimeout(() => {
              printWindow.document.close();
              printWindow.focus();
              printWindow.print();
              printWindow.addEventListener("afterprint", () => {
                printWindow.close();
              });
            }, 1000);
          });
        } else {
          toast({
            title: "Error",
            description: "Failed to fetch quotation details. Please try again.",
            variant: "destructive",
          });
        }
      })
      .catch((error) => {
        console.error("Error fetching job card:", error);
        toast({
          title: "Error",
          description: "Failed to fetch quotation details. Please try again.",
          variant: "destructive",
        });
      });
  };
  const InitiateJobCard = (quotationId: number) => {
    




    navigate(`/job-card/initiate-from-quotation/${quotationId}`);
    // fetchAPI(`/gate-pass/quotation/initiate/${quotationId}`, {
    //   method: "POST",
    // }).then((response) => {
    //   if (response.ok) {
    //     fetchQuotations("");
    //     toast({
    //       title: "Job Card Initiated",
    //       description:
    //         "The job card has been initiated and sent to the customer",
    //     });
    //   } else {
    //     toast({
    //       title: "Failed to initiate job card",
    //       description:
    //         "There was an error initiating the job card. Please try again.",
    //       variant: "destructive",
    //     });
    //   }
    // });
  };

  return (
    <div className="p-6 space-y-6">
      <div className=" max-w-12xl flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Quotations</h1>
          <p className="text-gray-600">
            Manage customer quotations and estimates
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Quotation
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-6xl h-[90vh] flex flex-col">
            <DialogHeader>
              <DialogTitle className="text-2xl font-semibold">
                Create Quotation
              </DialogTitle>
            </DialogHeader>

            {/* Search Section - fixed at top */}
            <div className="sticky top-0 z-10 bg-white pt-2 pb-4 border-b border-gray-200">
              <Label
                htmlFor="licensePlateSearch"
                className="text-sm font-medium text-gray-700 block mb-2"
              >
                Search License Plate
              </Label>
              <div className="flex gap-2">
                <Input
                  id="licensePlateSearch"
                  type="text"
                  placeholder="Enter license plate"
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    searchLicensePlate(e.target.value);
                  }}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => searchLicensePlate(searchTerm)}
                >
                  Search
                </Button>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto mt-4">
              {filteredOptions.length > 0 ? (
                <div className="rounded-lg border border-gray-200 shadow-sm overflow-x-auto">
                  <table className="min-w-full table-auto divide-y divide-gray-200 text-sm">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left font-medium text-gray-600">
                          Vehicle Number
                        </th>
                        <th className="px-4 py-2 text-left font-medium text-gray-600">
                          Customer Name
                        </th>
                        <th className="px-4 py-2 text-left font-medium text-gray-600">
                          Make & Model
                        </th>
                        <th className="px-4 py-2 text-left font-medium text-gray-600">
                          Created At
                        </th>
                        <th className="px-4 py-2 font-medium text-gray-600 text-center">
                          Action
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-100 bg-white">
                      {filteredOptions.map((option, idx) => {
                        return (
                          <tr
                            key={option.id || idx}
                            className="hover:bg-gray-50"
                          >
                            <td className="px-4 py-2">
                              {option.vehicleNumber ||
                                option.vehicleRegistrationNumber}
                            </td>
                            <td className="px-4 py-2">{option.customerName}</td>
                            <td className="px-4 py-2">
                              {/* Use available vehicle data or fallback to placeholder */}
                              {option.make || "—"} {option.model_model || "—"}
                            </td>
                            <td className="px-4 py-2">
                              {option?.createdAt
                                ? format(
                                    new Date(option.createdAt),
                                    "yyyy-MM-dd"
                                  )
                                : "—"}
                            </td>
                            <td className="px-4 py-2 text-center">
                              <Button
                                size="sm"
                                onClick={() => {
                                  navigate(`/quotation/${option.id}`);
                                }}
                              >
                                Create Quotation
                              </Button>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="flex flex-col justify-center items-center gap-10 text-gray-500 text-sm mt-6 text-center">
                  No results found. Try searching for a license plate.

                <Button
                  className="w-32"
                  onClick={() => navigate("/quotation/new")}
                >
                  <PlusCircle className="h-4 w-4" /> Create New
                </Button>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Total Quotations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{quotations?.length || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Pending Approval
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {quotations?.filter((q: any) => q.status === "pending").length ||
                0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Approved
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {quotations?.filter((q: any) => q.status === "approved").length ||
                0}
            </div>
          </CardContent>
        </Card>

        {/* <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">This Month Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              AED {quotations?.reduce((sum: number, q: any) => sum + (q.total || 0), 0).toFixed(2) || "0.00"}
            </div>
          </CardContent>
        </Card> */}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Quotations List</CardTitle>
          <div className="w-full justify-end flex">
          <Button
            onClick={() => setShowCreateDialog(true)}
            variant="outline"
            className=" w-24 items-center gap-2"
          >
            Refresh
          </Button></div>
        </CardHeader>
        <CardContent>
          {quotations && quotations.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3">Quotation #</th>
                    <th className="text-left p-3">Customer</th>
                    <th className="text-left p-3">Vehicle</th>
                    <th className="text-left p-3">Total</th>
                    <th className="text-left p-3">Status</th>
                    <th className="text-left p-3">Created At</th>
                    <th className="text-left p-3">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {quotations.map((quotation: any) => (
                    <tr
                      key={quotation.id}
                      className="border-b hover:bg-gray-50"
                    >
                      <td className="p-3 font-medium">{quotation.id}</td>
                      <td className="p-3">{quotation.customerName}</td>
                      <td className="p-3">
                        {quotation.vehicleRegistrationNumber}
                      </td>
                      <td className="p-3">
                        AED {quotation.total?.toFixed(2) || "0.00"}
                      </td>
                      <td className="p-3">
                        <Badge className={getStatusColor(quotation.status)}>
                          {quotation.status.toUpperCase()}
                        </Badge>
                      </td>
                      <td className="p-3">
                        {format(new Date(quotation.createdAt), "MMM dd, yyyy")}
                      </td>
                      <td className="p-3">
                        <div className="flex items-center gap-2">
                          {quotation.status === "pending" && (
                            <Button
                              variant="default"
                              size="sm"
                              onClick={() => {
                                revisitQuotation(quotation.id);
                              }}
                            >
                              <SquarePen className="h-4 w-4" /> REVISIT
                            </Button>
                          )}

                          {quotation.status === "pending" && (
                            <Button
                              onClick={() => {
                                approveQuotation(quotation.id);
                              }}
                              variant="secondary"
                              size="sm"
                            >
                              <FileCheck className="h-4 w-4" /> APPROVE
                            </Button>
                          )}
                          {quotation.status === "approved" && (
                            <Button
                              variant="default"
                              size="sm"
                              onClick={() => InitiateJobCard(quotation.id)}
                            >
                              <Send className="h-4 w-4" /> INITIATE JOBCARD
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <Calculator className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No quotations found</p>
              <Button onClick={() => setShowCreateDialog(true)}>
                Create Your First Quotation
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Item Dialog */}
      <Dialog open={showAddItemDialog} onOpenChange={setShowAddItemDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Item to Quotation</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Item Type</Label>
              <Select
                value={itemType}
                onValueChange={(value: "service" | "part") =>
                  setItemType(value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="service">Service</SelectItem>
                  <SelectItem value="part">Part</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {itemType === "service" ? (
              <div className="space-y-2">
                <Label>Service</Label>
                <Select
                  value={itemForm.serviceId}
                  onValueChange={(value) =>
                    setItemForm({ ...itemForm, serviceId: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select service" />
                  </SelectTrigger>
                  <SelectContent>
                    {services?.map((service: any) => (
                      <SelectItem
                        key={service.id}
                        value={service.id.toString()}
                      >
                        {service.name} - AED {service.price}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ) : (
              <div className="space-y-2">
                <Label>Part</Label>
                <Select
                  value={itemForm.partId}
                  onValueChange={(value) =>
                    setItemForm({ ...itemForm, partId: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select part" />
                  </SelectTrigger>
                  <SelectContent>
                    {parts?.map((part: any) => (
                      <SelectItem key={part.id} value={part.id.toString()}>
                        {part.name} - AED {part.price}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Quantity</Label>
                <Input
                  type="number"
                  min="1"
                  value={itemForm.quantity}
                  onChange={(e) =>
                    setItemForm({ ...itemForm, quantity: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label>Price (AED)</Label>
                <Input
                  type="number"
                  step="0.01"
                  value={itemForm.price}
                  onChange={(e) =>
                    setItemForm({ ...itemForm, price: e.target.value })
                  }
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Description</Label>
              <Textarea
                placeholder="Additional description"
                value={itemForm.description}
                onChange={(e) =>
                  setItemForm({ ...itemForm, description: e.target.value })
                }
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowAddItemDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleAddItem}>Add Item</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Quotations;
